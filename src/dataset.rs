use candle_core::{<PERSON><PERSON>, Result, Tensor};
use polars::prelude::*;

/// Loads data from a Parquet file and converts it to a Candle tensor.
/// Returns a 2D tensor with shape [timesteps, num_cryptocurrencies]
pub fn load_and_prepare_data(
    path: &str,
    device: &Device,
) -> Result<Tensor> {
    // Load the parquet file
    let df = LazyFrame::scan_parquet(path, Default::default())
        .expect("Failed to scan Parquet file")
        .collect()
        .expect("Failed to collect LazyFrame");

    println!("Data loaded. Shape: {} rows × {} columns", df.height(), df.width());

    // Convert DataFrame to Vec<Vec<f32>>
    let num_rows = df.height();
    let num_cols = df.width();
    let mut data_vec: Vec<f32> = Vec::with_capacity(num_rows * num_cols);

    // Iterate through columns and collect data
    for col in df.get_columns() {
        match col.dtype() {
            DataType::Float64 => {
                let float_series = col.f64().expect("Failed to convert to f64");
                for value in float_series.iter() {
                    data_vec.push(value.unwrap_or(0.0) as f32);
                }
            },
            DataType::Float32 => {
                let float_series = col.f32().expect("Failed to convert to f32");
                for value in float_series.iter() {
                    data_vec.push(value.unwrap_or(0.0));
                }
            },
            DataType::Int64 => {
                let int_series = col.i64().expect("Failed to convert to i64");
                for value in int_series.iter() {
                    data_vec.push(value.unwrap_or(0) as f32);
                }
            },
            _ => {
                // Try to cast to f64 first
                let casted = col.cast(&DataType::Float64).expect("Failed to cast to f64");
                let float_series = casted.f64().expect("Failed to convert to f64");
                for value in float_series.iter() {
                    data_vec.push(value.unwrap_or(0.0) as f32);
                }
            }
        }
    }

    // Create tensor from the flattened data
    // Note: Polars stores data column-wise, so we need to transpose
    let tensor = Tensor::from_vec(data_vec, (num_cols, num_rows), device)?;
    let transposed_tensor = tensor.transpose(0, 1)?; // Transpose to [rows, cols]

    println!("Data tensor shape: {:?}", transposed_tensor.shape());
    Ok(transposed_tensor)
}