use candle_core::{<PERSON><PERSON>, <PERSON>sul<PERSON>, Tensor};
use polars::prelude::*;

/// Configuration for data loading
#[derive(Debug, <PERSON>lone)]
pub struct DataConfig {
    /// Number of time series (cryptocurrencies) to use
    pub num_time_series: usize,
    /// Maximum number of timesteps to load (None = load all)
    pub max_timesteps: Option<usize>,
    /// Whether to normalize the data
    pub normalize: bool,
    /// Fill strategy for missing values
    pub fill_strategy: FillStrategy,
}

#[derive(Debug, Clone)]
pub enum FillStrategy {
    Zero,
    Forward,
    Mean,
}

impl Default for DataConfig {
    fn default() -> Self {
        Self {
            num_time_series: 100,
            max_timesteps: None,
            normalize: false,
            fill_strategy: FillStrategy::Zero,
        }
    }
}

/// Loads data from a Parquet file containing cryptocurrency return data,
/// handles missing values, and converts it to a Candle tensor.
///
/// Expected data format: Parquet file with columns ending in '_return'
/// representing price differences for different cryptocurrencies.
///
/// Returns a tensor with shape [timesteps, num_cryptocurrencies]
pub fn load_and_prepare_data(
    path: &str,
    device: &Device,
    config: &DataConfig,
) -> Result<Tensor> {
    println!("🔍 Loading data from: {}", path);

    // Load the parquet file
    let df_lazy = LazyFrame::scan_parquet(path, Default::default())
        .map_err(|e| candle_core::Error::Msg(format!("Failed to scan Parquet file: {}", e)))?;

    // Collect to DataFrame to inspect columns
    let df = df_lazy
        .collect()
        .map_err(|e| candle_core::Error::Msg(format!("Failed to collect LazyFrame: {}", e)))?;

    println!("📊 Original data shape: {} rows × {} columns", df.height(), df.width());

    // Get cryptocurrency columns (those ending with '_return')
    let crypto_columns: Vec<String> = df.get_column_names()
        .iter()
        .filter(|name| name.ends_with("_return"))
        .map(|s| s.to_string())
        .collect();

    if crypto_columns.is_empty() {
        return Err(candle_core::Error::Msg(
            "No cryptocurrency columns found (expected columns ending with '_return')".to_string()
        ));
    }

    println!("💰 Found {} cryptocurrency columns", crypto_columns.len());

    // Select the desired number of cryptocurrencies
    let selected_columns: Vec<String> = crypto_columns
        .into_iter()
        .take(config.num_time_series)
        .collect();

    println!("📈 Using {} cryptocurrencies for training", selected_columns.len());

    // Select only the cryptocurrency columns
    let crypto_df = df.select(&selected_columns)
        .map_err(|e| candle_core::Error::Msg(format!("Failed to select columns: {}", e)))?;

    // Apply timestep limit if specified
    let final_df = if let Some(max_timesteps) = config.max_timesteps {
        let rows_to_take = max_timesteps.min(crypto_df.height());
        let start_row = crypto_df.height().saturating_sub(rows_to_take);
        crypto_df.slice(start_row as i64, rows_to_take)
    } else {
        crypto_df
    };

    println!("⏰ Final data shape: {} rows × {} columns", final_df.height(), final_df.width());

    // Handle missing values based on strategy
    let cleaned_df = match config.fill_strategy {
        FillStrategy::Zero => {
            final_df.lazy()
                .fill_null(lit(0.0))
                .collect()
                .map_err(|e| candle_core::Error::Msg(format!("Failed to fill nulls with zero: {}", e)))?
        },
        FillStrategy::Forward => {
            final_df.lazy()
                .fill_null(FillNullStrategy::Forward(None))
                .fill_null(lit(0.0)) // Fill any remaining nulls at the beginning
                .collect()
                .map_err(|e| candle_core::Error::Msg(format!("Failed to forward fill: {}", e)))?
        },
        FillStrategy::Mean => {
            // Calculate mean for each column and fill nulls
            let mut filled_df = final_df.clone();
            for col_name in &selected_columns {
                let mean_val = final_df.column(col_name)
                    .map_err(|e| candle_core::Error::Msg(format!("Failed to get column {}: {}", col_name, e)))?
                    .mean()
                    .unwrap_or(0.0);

                filled_df = filled_df.lazy()
                    .with_columns([
                        col(col_name).fill_null(lit(mean_val))
                    ])
                    .collect()
                    .map_err(|e| candle_core::Error::Msg(format!("Failed to fill with mean: {}", e)))?;
            }
            filled_df
        }
    };

    // Convert to ndarray
    let returns_ndarray = cleaned_df
        .to_ndarray::<Float64Type>(IndexOrder::Fortran)
        .map_err(|e| candle_core::Error::Msg(format!("Failed to convert to ndarray: {}", e)))?;

    // Convert to tensor and cast to f32
    let returns_tensor = Tensor::from_ndarray(
        returns_ndarray.mapv(|x| x as f32)
    ).to_device(device)?;

    println!("✅ Data loaded and prepared. Shape: {:?}", returns_tensor.shape());
    println!("📊 Data range: min={:.6}, max={:.6}",
             returns_tensor.min(0)?.min(0)?.to_scalar::<f32>()?,
             returns_tensor.max(0)?.max(0)?.to_scalar::<f32>()?);

    Ok(returns_tensor)
}

/// Creates a sliding window dataset from the time series data
/// Returns batches of shape [batch_size, sequence_length, num_features]
pub fn create_sliding_windows(
    data: &Tensor,
    sequence_length: usize,
    batch_size: usize,
) -> Result<Vec<Tensor>> {
    let shape = data.shape();
    let total_timesteps = shape[0];
    let num_features = shape[1];

    if total_timesteps < sequence_length {
        return Err(candle_core::Error::Msg(
            format!("Not enough data: need at least {} timesteps, got {}",
                   sequence_length, total_timesteps)
        ));
    }

    let num_windows = total_timesteps - sequence_length + 1;
    let num_batches = (num_windows + batch_size - 1) / batch_size; // Ceiling division

    let mut batches = Vec::new();

    for batch_idx in 0..num_batches {
        let start_window = batch_idx * batch_size;
        let end_window = ((batch_idx + 1) * batch_size).min(num_windows);
        let actual_batch_size = end_window - start_window;

        let mut batch_windows = Vec::new();

        for window_idx in start_window..end_window {
            let window = data.narrow(0, window_idx, sequence_length)?;
            batch_windows.push(window);
        }

        // Stack windows to create batch
        let batch = Tensor::stack(&batch_windows, 0)?;
        batches.push(batch);
    }

    println!("🔄 Created {} batches of sliding windows", batches.len());
    println!("📏 Each batch shape: [batch_size, sequence_length, num_features]");

    Ok(batches)
}